package com.yunqu.park.iot.netty.codec;

import com.yunqu.park.common.core.utils.SpringUtils;
import com.yunqu.park.iot.config.IotProtocolConfig;
import com.yunqu.park.iot.constant.IotConstants;
import com.yunqu.park.iot.netty.protocol.*;
import com.yunqu.park.iot.pool.IotMessagePool;
import com.yunqu.park.iot.utils.ByteBufUtils;
import com.yunqu.park.iot.utils.CrcUtils;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import lombok.extern.slf4j.Slf4j;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * GT06协议解码器
 *
 * <AUTHOR>
 */
@Slf4j
public class GT06ProtocolDecoder extends ByteToMessageDecoder {

    @Override
    public void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        // 记录原始数据用于调试
        int originalReadableBytes = in.readableBytes();
        log.info("[RAW-DATA] {} Length={}", "IN", originalReadableBytes);

        // 在解码前打印接收到的完整原始数据（不影响后续解码）
        if (originalReadableBytes > 0) {
            // 复制一份ByteBuf以不影响原始数据的读取位置
            ByteBuf copy = in.copy();
            byte[] rawData = new byte[copy.readableBytes()];
            copy.readBytes(rawData);

            // 无论日志级别如何，都输出原始数据的十六进制表示
            log.info("[RAW-TCP-IN] 收到来自[{}]的原始数据: {} 字节\n原始十六进制: {}\n格式化显示:\n{}",
                ctx.channel().remoteAddress(),
                rawData.length,
                ByteBufUtils.bytesToHexString(rawData),
                ByteBufUtil.prettyHexDump(in.duplicate()));

            // 释放复制的ByteBuf
            copy.release();
        }

        if (originalReadableBytes < IotConstants.GT06Protocol.MIN_PACKET_LENGTH) {
            log.debug("[PROTOCOL-DECODE] Insufficient data: available={}, required={}",
                originalReadableBytes, IotConstants.GT06Protocol.MIN_PACKET_LENGTH);
            return; // 数据不足，等待更多数据
        }

        in.markReaderIndex();

        try {
            log.debug("[PROTOCOL-DECODE] 🔍 开始解码数据包: RemoteAddress={}, AvailableBytes={}",
                ctx.channel().remoteAddress(), originalReadableBytes);

            // 1. 检查起始位 (支持0x7878和0x7979)
            byte[] startFlag = ByteBufUtils.safeReadBytes(in, 2);
            if (startFlag == null) {
                in.resetReaderIndex();
                log.debug("[PROTOCOL-DECODE] 数据不足，无法读取起始位，等待更多数据");
                return;
            }

            if (!Arrays.equals(startFlag, IotConstants.GT06Protocol.START_FLAG_7878) &&
                !Arrays.equals(startFlag, IotConstants.GT06Protocol.START_FLAG_7979)) {
                in.resetReaderIndex();
                in.skipBytes(1); // 跳过一个字节继续寻找
                log.debug("[PROTOCOL-DECODE] 无效起始位: {}, 跳过1字节继续寻找",
                    ByteBufUtils.bytesToHexString(startFlag));
                return;
            }

            log.debug("[PROTOCOL-DECODE] ✅ 起始位验证通过: {}", ByteBufUtils.bytesToHexString(startFlag));

            // 2. 读取包长度
            Byte packetLengthByte = ByteBufUtils.safeReadByte(in);
            if (packetLengthByte == null) {
                in.resetReaderIndex();
                log.debug("[PROTOCOL-DECODE] 数据不足，无法读取包长度字节");
                return;
            }

            byte packetLength = packetLengthByte;
            int packetLengthValue = packetLength & 0xFF;
            log.debug("[PROTOCOL-DECODE] 📏 包长度字段值: {}, 剩余可读字节: {}",
                packetLengthValue, in.readableBytes());

            // 3. 修复：正确验证数据完整性
            // GT06协议：包长度字段 = 协议号(1) + 内容(N) + 序列号(2) + CRC(2)
            // 总数据包 = 起始位(2) + 包长度(1) + 数据内容(packetLengthValue) + 停止位(2)
            int remainingDataLength = packetLengthValue; // 包长度字段的值就是剩余数据长度
            if (in.readableBytes() < remainingDataLength + 2) { // +2 for stop flag
                in.resetReaderIndex();
                log.debug("[PROTOCOL-DECODE] 数据包不完整: 需要{}字节(数据{}+停止位2), 可用{}字节, 等待更多数据",
                    remainingDataLength + 2, remainingDataLength, in.readableBytes());
                return;
            }

            log.debug("[PROTOCOL-DECODE] ✅ 数据完整性验证通过: 需要{}字节, 可用{}字节",
                remainingDataLength + 2, in.readableBytes());

            // 4. 修复：一次性读取所有数据（不包含停止位）
            byte[] packetData = ByteBufUtils.safeReadBytes(in, remainingDataLength);
            if (packetData == null) {
                in.resetReaderIndex();
                log.warn("[PROTOCOL-DECODE] ❌ 无法读取数据包内容: 需要{}字节, 可用{}字节",
                    remainingDataLength, in.readableBytes());
                return;
            }

            log.debug("[PROTOCOL-DECODE] ✅ 数据包内容读取成功: {}字节, 数据={}",
                packetData.length, ByteBufUtils.bytesToHexString(packetData));

            // 5. 修复：验证停止位（不抛异常，改为跳过处理）
            byte[] stopFlag = ByteBufUtils.safeReadBytes(in, 2);
            if (stopFlag == null) {
                in.resetReaderIndex();
                log.warn("[PROTOCOL-DECODE] ❌ 无法读取停止位: 可用{}字节", in.readableBytes());
                return;
            }

            if (!Arrays.equals(stopFlag, IotConstants.GT06Protocol.STOP_FLAG)) {
                log.warn("[PROTOCOL-DECODE] ⚠️ 停止位不匹配: 期望={}, 实际={}, 跳过此数据包",
                    ByteBufUtils.bytesToHexString(IotConstants.GT06Protocol.STOP_FLAG),
                    ByteBufUtils.bytesToHexString(stopFlag));
                // 不抛异常，跳过这个字节继续处理
                in.resetReaderIndex();
                in.skipBytes(1);
                return;
            }

            log.debug("[PROTOCOL-DECODE] ✅ 停止位验证通过: {}", ByteBufUtils.bytesToHexString(stopFlag));

            // 6. 修复：CRC校验（宽松模式，失败不中断处理）
            boolean crcValid = performCrcValidation(packetData, ctx);
            log.debug("[PROTOCOL-DECODE] CRC校验结果: {}", crcValid ? "通过" : "失败(继续处理)");

            // 7. 解析协议内容并构造消息对象
            IotMessage message = parseMessage(startFlag, packetData, ctx);
            if (message != null) {
                out.add(message);
                log.info("[PROTOCOL-DECODE] ✅ 数据包解码成功: Protocol=0x{}, IMEI={}, SequenceNumber={}, RemoteAddress={}",
                    String.format("%02X", message.getProtocol() & 0xFF),
                    message.getImei(), message.getSequenceNumber(), ctx.channel().remoteAddress());

                // P0 阶段核心兼容性改造: 实现登录和心跳的响应
                handleResponse(ctx, message);

            } else {
                log.warn("[PROTOCOL-DECODE] ❌ 消息解析失败: RemoteAddress={}, 数据={}",
                    ctx.channel().remoteAddress(), ByteBufUtils.bytesToHexString(packetData));
            }

        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] ❌ 解码过程异常: RemoteAddress={}, Error={}, OriginalBytes={}, CurrentBytes={}",
                ctx.channel().remoteAddress(), e.getMessage(), originalReadableBytes, in.readableBytes(), e);

            // 输出原始数据用于调试
            in.resetReaderIndex();
            if (in.readableBytes() > 0) {
                String hexData = ByteBufUtils.bufToHexString(in, Math.min(64, in.readableBytes()));
                log.debug("[PROTOCOL-DECODE] 原始数据(前64字节): {}", hexData);
            }

            // 跳过一个字节继续尝试解析，不抛异常中断连接
            if (in.readableBytes() > 0) {
                in.skipBytes(1);
                log.debug("[PROTOCOL-DECODE] 跳过1字节，继续解码处理");
            }
        }
    }

    /**
     * 根据消息类型处理响应
     */
    private void handleResponse(ChannelHandlerContext ctx, IotMessage message) {
        byte protocol = message.getProtocol();
        if (protocol == IotConstants.GT06Protocol.PROTOCOL_LOGIN || protocol == IotConstants.GT06Protocol.PROTOCOL_HEARTBEAT) {
            ByteBuf response = createResponse(ctx, protocol, message.getSequenceNumber());
            ctx.writeAndFlush(response);
            log.info("[PROTOCOL-RESPONSE] ✅ 发送响应: Protocol=0x{}, SequenceNumber={}, RemoteAddress={}",
                String.format("%02X", protocol & 0xFF), message.getSequenceNumber(), ctx.channel().remoteAddress());
        }
    }

    /**
     * 创建响应包
     */
    private ByteBuf createResponse(ChannelHandlerContext ctx, byte protocol, int sequenceNumber) {
        ByteBuf buffer = ctx.alloc().buffer(8); // 使用上下文的分配器创建ByteBuf
        buffer.writeBytes(IotConstants.GT06Protocol.START_FLAG_7878);
        buffer.writeByte(0x05); // 响应包长度固定为5
        buffer.writeByte(protocol);
        buffer.writeShort(sequenceNumber);
        // 注意: concox-master的响应似乎没有CRC，这里为了兼容也暂时不加
        buffer.writeBytes(IotConstants.GT06Protocol.STOP_FLAG);
        return buffer;
    }


    /**
     * 解析消息内容
     * 修复：正确解析GT06协议数据包结构
     */
    private IotMessage parseMessage(byte[] startFlag, byte[] packetData, ChannelHandlerContext ctx) {
        if (packetData.length < 4) {
            log.warn("[PROTOCOL-DECODE] 数据包太短，无法解析: 长度={}", packetData.length);
            return null;
        }

        try {
            // 阶段2优化：使用对象池获取消息对象
            IotMessagePool messagePool = SpringUtils.getBean(IotMessagePool.class);
            IotMessage message = messagePool != null ? messagePool.borrowMessage() : new IotMessage();
            message.setStartFlag(startFlag);

            // GT06协议数据包结构：包长度(1) + 协议号(1) + 内容(N) + 序列号(2) + CRC(2)
            message.setPacketLength(packetData[0] & 0xFF);

            // 解析协议号
            byte protocol = packetData[1];
            message.setProtocol(protocol);

            log.debug("[PROTOCOL-DECODE] 解析协议号: 0x{} ({})",
                String.format("%02X", protocol & 0xFF), message.getProtocolName());

            // 修复：正确计算内容长度
            // 数据包结构：包长度(1) + 协议号(1) + 内容(N) + 序列号(2) + CRC(2)
            int contentLength = packetData.length - 5; // 减去包长度(1) + 协议号(1) + 序列号(2) + CRC(2)
            if (contentLength > 0) {
                byte[] content = new byte[contentLength];
                System.arraycopy(packetData, 2, content, 0, contentLength);
                message.setContent(content);

                log.debug("[PROTOCOL-DECODE] 内容数据: 长度={}, 数据={}",
                    contentLength, ByteBufUtils.bytesToHexString(content));

                // 根据不同协议解析内容
                switch (protocol) {
                    case IotConstants.GT06Protocol.PROTOCOL_LOGIN:
                        if (contentLength >= 8) {
                            String imei = parseImeiFromLoginPacket(content);
                            message.setImei(imei);
                            log.debug("[PROTOCOL-DECODE] 解析登录包IMEI: {}", imei);
                        }
                        break;
                    case IotConstants.GT06Protocol.PROTOCOL_HEARTBEAT:
                    case IotConstants.GT06Protocol.PROTOCOL_STATUS_INFO:
                        if (contentLength >= 1) {
                            StatusData statusData = StatusData.from(content[0]);
                            message.setStatusData(statusData);
                            log.debug("[PROTOCOL-DECODE] 解析状态/心跳包完成: {}", statusData);
                        }
                        break;
                    case IotConstants.GT06Protocol.PROTOCOL_LOCATION:
                        // P0: 实现GPS定位包的完整解析
                        if (contentLength >= 18) { // 至少需要18字节
                            GpsData gpsData = parseGpsData(content);
                            message.setGpsData(gpsData);
                            log.debug("[PROTOCOL-DECODE] 解析GPS定位包完成: {}", gpsData);
                        } else {
                            log.warn("[PROTOCOL-DECODE] GPS定位包长度不足: 需要18, 实际{}", contentLength);
                        }
                        break;
                    case IotConstants.GT06Protocol.PROTOCOL_ALARM:
                        // P1: 实现报警包的完整解析
                        if (contentLength >= 27) { // GPS(18) + LBS(7) + Status(1) + Alarm(1)
                            parseAlarmData(content, message);
                            log.debug("[PROTOCOL-DECODE] 解析报警包完成");
                        } else {
                            log.warn("[PROTOCOL-DECODE] 报警包长度不足: 需要27+, 实际{}", contentLength);
                        }
                        break;
                    case IotConstants.GT06Protocol.PROTOCOL_COMBINED_INFO:
                        // P2: 实现组合信息包的解析
                        if (contentLength >= 26) { // GPS(18) + LBS(7) + Status(1)
                            parseCombinedInfoData(content, message);
                            log.debug("[PROTOCOL-DECODE] 解析组合信息包完成");
                        } else {
                            log.warn("[PROTOCOL-DECODE] 组合信息包长度不足: 需要26+, 实际{}", contentLength);
                        }
                        break;
                    case IotConstants.GT06Protocol.PROTOCOL_LBS_INFO:
                        // P2: 实现LBS信息包的解析
                        if (contentLength >= 9) { // MCC(2) + MNC(1) + LAC(2) + CellID(4)
                            message.setLbsData(parseLbsData(content));
                            log.debug("[PROTOCOL-DECODE] 解析LBS信息包完成");
                        } else {
                            log.warn("[PROTOCOL-DECODE] LBS信息包长度不足: 需要9, 实际{}", contentLength);
                        }
                        break;
                    case IotConstants.GT06Protocol.PROTOCOL_IMSI:
                        // P2: 解析IMSI
                        message.setImsi(new String(content, StandardCharsets.US_ASCII).trim());
                        log.debug("[PROTOCOL-DECODE] 解析IMSI完成: {}", message.getImsi());
                        break;
                    case IotConstants.GT06Protocol.PROTOCOL_ICCID:
                        // P2: 解析ICCID
                        message.setIccid(new String(content, StandardCharsets.US_ASCII).trim());
                        log.debug("[PROTOCOL-DECODE] 解析ICCID完成: {}", message.getIccid());
                        break;
                    case IotConstants.GT06Protocol.PROTOCOL_LBS_WIFI:
                        // P2: 实现LBS+WIFI信息包的解析
                        parseLbsWifiData(content, message);
                        log.debug("[PROTOCOL-DECODE] 解析LBS+WIFI信息包完成");
                        break;
                    case IotConstants.GT06Protocol.PROTOCOL_TERMINAL_RESPONSE:
                        // P2: 实现终端指令响应的解析
                        if (contentLength >= 4) {
                            parseCommandResponseData(content, message);
                            log.debug("[PROTOCOL-DECODE] 解析终端指令响应完成");
                        } else {
                            log.warn("[PROTOCOL-DECODE] 终端指令响应包长度不足: 需要4+, 实际{}", contentLength);
                        }
                        break;
                    default:
                        log.debug("[PROTOCOL-DECODE] 接收到未处理协议: 0x{}", String.format("%02X", protocol & 0xFF));
                        break;
                }
            }

            // 解析序列号（大端序）
            int sequenceNumber = ((packetData[packetData.length - 4] & 0xFF) << 8) |
                (packetData[packetData.length - 3] & 0xFF);
            message.setSequenceNumber(sequenceNumber);

            // 解析CRC（大端序）
            int crc = ((packetData[packetData.length - 2] & 0xFF) << 8) |
                (packetData[packetData.length - 1] & 0xFF);
            message.setCrc(crc);

            message.setStopFlag(IotConstants.GT06Protocol.STOP_FLAG);

            // 设置客户端信息
            String remoteAddress = ctx.channel().remoteAddress().toString();
            if (remoteAddress.startsWith("/")) {
                remoteAddress = remoteAddress.substring(1);
            }
            String[] parts = remoteAddress.split(":");
            message.setClientIp(parts[0]);
            if (parts.length > 1) {
                try {
                    message.setClientPort(Integer.parseInt(parts[1]));
                } catch (NumberFormatException e) {
                    message.setClientPort(0);
                }
            }

            log.debug("[PROTOCOL-DECODE] 消息解析完成: Protocol=0x{}, IMEI={}, SequenceNumber={}, CRC=0x{}",
                String.format("%02X", protocol & 0xFF), message.getImei(),
                sequenceNumber, String.format("%04X", crc));

            return message;

        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] 消息解析异常: {}, 数据={}",
                e.getMessage(), ByteBufUtils.bytesToHexString(packetData), e);
            return null;
        }
    }

    /**
     * 从登录包中解析IMEI
     */
    private String parseImeiFromLoginPacket(byte[] content) {
        if (content.length < 8) {
            return null;
        }

        // IMEI通常在登录包的前8个字节，以BCD编码
        StringBuilder imei = new StringBuilder();
        for (int i = 0; i < 8; i++) {
            int high = (content[i] >> 4) & 0x0F;
            int low = content[i] & 0x0F;
            imei.append(high).append(low);
        }

        // 移除最后一位校验位，IMEI为15位
        String result = imei.toString();
        return result.length() > 15 ? result.substring(0, 15) : result;
    }

    /**
     * 执行CRC校验
     * 修复：采用宽松模式，校验失败不中断处理流程
     *
     * @param packetData 数据包内容
     * @param ctx        通道上下文
     * @return 校验是否通过
     */
    private boolean performCrcValidation(byte[] packetData, ChannelHandlerContext ctx) {
        try {
            // 获取配置
            IotProtocolConfig config = getProtocolConfig();

            if (!config.getCrc().isEnabled()) {
                log.debug("[PROTOCOL-DECODE] CRC校验已禁用");
                return true;
            }

            boolean crcValid;
            if (config.getCrc().isLenientMode()) {
                crcValid = CrcUtils.validateCrcLenient(packetData);
                log.debug("[PROTOCOL-DECODE] 使用宽松模式CRC校验");
            } else {
                crcValid = CrcUtils.validateCrc(packetData);
                log.debug("[PROTOCOL-DECODE] 使用严格模式CRC校验");
            }

            if (!crcValid) {
                if (config.getCrc().isVerboseLogging()) {
                    log.warn("[PROTOCOL-DECODE] ⚠️ CRC校验失败: RemoteAddress={}, PacketLength={}, Data={}",
                        ctx.channel().remoteAddress(), packetData.length,
                        ByteBufUtils.bytesToHexString(packetData));
                }

                // 修复：即使CRC校验失败也继续处理，不抛异常
                log.warn("[PROTOCOL-DECODE] ⚠️ CRC校验失败，但继续处理数据包 (宽松模式)");
                return false;
            } else {
                if (config.getCrc().isVerboseLogging()) {
                    log.debug("[PROTOCOL-DECODE] ✅ CRC校验通过");
                }
                return true;
            }

        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] CRC校验异常: {}, 继续处理数据包", e.getMessage(), e);
            // 异常情况下也继续处理，不中断数据流
            return false;
        }
    }

    /**
     * 获取协议配置
     *
     * @return 协议配置对象
     */
    private IotProtocolConfig getProtocolConfig() {
        try {
            return SpringUtils.getBean(IotProtocolConfig.class);
        } catch (Exception e) {
            log.debug("Failed to get protocol config, using defaults: {}", e.getMessage());
            // 返回默认配置
            IotProtocolConfig defaultConfig = new IotProtocolConfig();
            defaultConfig.getCrc().setEnabled(true);
            defaultConfig.getCrc().setLenientMode(true);
            defaultConfig.getCrc().setContinueOnFailure(true);
            defaultConfig.getCrc().setVerboseLogging(true);
            return defaultConfig;
        }
    }

    /**
     * 解析GPS定位包 (协议0x12)
     */
    private GpsData parseGpsData(byte[] content) {
        ByteBuffer buffer = ByteBuffer.wrap(content);

        // 1. 解析日期时间 (6字节 BCD)
        byte[] timeBytes = new byte[6];
        buffer.get(timeBytes);
        LocalDateTime dateTime = parseBcdDateTime(timeBytes);

        // 2. 解析GPS信息 (1字节)
        byte gpsInfo = buffer.get();
        int satellites = gpsInfo & 0x0F; // 低4位是卫星数

        // 3. 解析纬度 (4字节)
        double latitude = buffer.getInt() / 1800000.0; // 30000 * 60

        // 4. 解析经度 (4字节)
        double longitude = buffer.getInt() / 1800000.0; // 30000 * 60

        // 5. 解析速度 (1字节)
        int speed = buffer.get() & 0xFF;

        // 6. 解析状态和航向 (2字节)
        int statusAndCourse = buffer.getShort() & 0xFFFF;
        int course = statusAndCourse & 0x03FF; // 低10位是航向

        return GpsData.builder()
            .datetime(dateTime)
            .satellites(satellites)
            .latitude(latitude)
            .longitude(longitude)
            .speed(speed)
            .course(course)
            .status(statusAndCourse)
            .build();
    }

    /**
     * 解析BCD编码的日期时间
     */
    private LocalDateTime parseBcdDateTime(byte[] bcd) {
        try {
            int year = 2000 + ((bcd[0] >> 4) * 10 + (bcd[0] & 0x0F));
            int month = ((bcd[1] >> 4) * 10 + (bcd[1] & 0x0F));
            int day = ((bcd[2] >> 4) * 10 + (bcd[2] & 0x0F));
            int hour = ((bcd[3] >> 4) * 10 + (bcd[3] & 0x0F));
            int minute = ((bcd[4] >> 4) * 10 + (bcd[4] & 0x0F));
            int second = ((bcd[5] >> 4) * 10 + (bcd[5] & 0x0F));
            return LocalDateTime.of(year, month, day, hour, minute, second);
        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] BCD日期时间解析失败", e);
            return LocalDateTime.now(); // 解析失败返回当前时间
        }
    }


    /**
     * 解析报警包 (协议0x26)
     * 这是一个复合包，包含GPS, LBS, Status和Alarm信息
     */
    private void parseAlarmData(byte[] content, IotMessage message) {
        ByteBuffer buffer = ByteBuffer.wrap(content);

        // 1. 解析GPS数据 (前18字节)
        byte[] gpsBytes = new byte[18];
        buffer.get(gpsBytes);
        message.setGpsData(parseGpsData(gpsBytes));

        // 2. 解析LBS数据 (7字节)
        int mcc = buffer.getShort() & 0xFFFF;
        int mnc = buffer.get() & 0xFF;
        int lac = buffer.getShort() & 0xFFFF;
        int cellId = buffer.getShort() & 0xFFFF; // 2字节CellID
        message.setLbsData(LbsData.builder().mcc(mcc).mnc(mnc).lac(lac).cellId(cellId).build());

        // 3. 解析状态和报警信息
        byte statusByte = buffer.get();
        message.setStatusData(StatusData.from(statusByte));

        byte alarmByte = buffer.get();
        message.setAlarmData(AlarmData.from(alarmByte));
    }

    /**
     * 解析组合信息包 (协议0x16)
     * 这是一个复合包，包含GPS, LBS, Status信息
     */
    private void parseCombinedInfoData(byte[] content, IotMessage message) {
        ByteBuffer buffer = ByteBuffer.wrap(content);

        // 1. 解析GPS数据 (前18字节)
        byte[] gpsBytes = new byte[18];
        buffer.get(gpsBytes);
        message.setGpsData(parseGpsData(gpsBytes));

        // 2. 解析LBS数据 (7字节)
        int mcc = buffer.getShort() & 0xFFFF;
        int mnc = buffer.get() & 0xFF;
        int lac = buffer.getShort() & 0xFFFF;
        int cellId = buffer.getShort() & 0xFFFF; // 2字节CellID
        message.setLbsData(LbsData.builder().mcc(mcc).mnc(mnc).lac(lac).cellId(cellId).build());

        // 3. 解析状态信息
        byte statusByte = buffer.get();
        message.setStatusData(StatusData.from(statusByte));
    }

    /**
     * 解析LBS信息包 (协议0x11)
     */
    private LbsData parseLbsData(byte[] content) {
        ByteBuffer buffer = ByteBuffer.wrap(content);
        int mcc = buffer.getShort() & 0xFFFF;
        int mnc = buffer.get() & 0xFF;
        int lac = buffer.getShort() & 0xFFFF;
        int cellId = buffer.getInt(); // 4字节CellID
        return LbsData.builder().mcc(mcc).mnc(mnc).lac(lac).cellId(cellId).build();
    }

    /**
     * 解析LBS+WIFI信息包 (协议0x2C)
     */
    private void parseLbsWifiData(byte[] content, IotMessage message) {
        ByteBuffer buffer = ByteBuffer.wrap(content);

        // 1. 解析LBS数据 (前9字节)
        int mcc = buffer.getShort() & 0xFFFF;
        int mnc = buffer.get() & 0xFF;
        int lac = buffer.getShort() & 0xFFFF;
        int cellId = buffer.getInt(); // 4字节CellID
        message.setLbsData(LbsData.builder().mcc(mcc).mnc(mnc).lac(lac).cellId(cellId).build());

        // 2. 解析WIFI数据
        int wifiCount = buffer.get() & 0xFF;
        List<WifiHotspot> hotspots = new ArrayList<>(wifiCount);
        for (int i = 0; i < wifiCount; i++) {
            if (buffer.remaining() < 7) { // MAC(6) + RSSI(1)
                log.warn("[PROTOCOL-DECODE] LBS+WIFI包数据不足，无法解析第{}个WIFI热点", i + 1);
                break;
            }
            byte[] macBytes = new byte[6];
            buffer.get(macBytes);
            String macAddress = ByteBufUtils.bytesToHexString(macBytes, ":");
            int signalStrength = buffer.get() & 0xFF;
            hotspots.add(WifiHotspot.builder().macAddress(macAddress).signalStrength(signalStrength).build());
        }
        message.setWifiHotspots(hotspots);
    }


    /**
     * 经纬度解析 - 按协议规范实现
     * 公式: (度数×60+分数)×30000 = 十进制值
     */
    public static double parseCoordinate(byte[] coordinateBytes) {
        if (coordinateBytes.length != 4) {
            return 0.0;
        }
        int value = ByteBuffer.wrap(coordinateBytes).getInt();
        return value / 1800000.0; // 30000 * 60
    }

    /**
     * 解析终端指令响应包 (协议0x15)
     */
    private void parseCommandResponseData(byte[] content, IotMessage message) {
        ByteBuffer buffer = ByteBuffer.wrap(content);

        // 1. 解析服务器标志位 (4字节)
        long serverFlag = buffer.getInt() & 0xFFFFFFFFL;

        // 2. 解析指令内容 (剩余部分)
        byte[] commandBytes = new byte[buffer.remaining()];
        buffer.get(commandBytes);
        String commandContent = new String(commandBytes, StandardCharsets.UTF_8);

        message.setCommandResponseData(CommandResponseData.builder()
            .serverFlag(serverFlag)
            .commandContent(commandContent)
            .build());
    }
}
