package com.yunqu.park.iot.netty.codec;

import com.yunqu.park.iot.netty.protocol.IotMessage;
import io.netty.buffer.Unpooled;
import io.netty.channel.embedded.EmbeddedChannel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GT06协议解码器完整功能测试
 * 验证修复和扩展后的解码器能够正确处理所有支持的协议数据包
 *
 * <AUTHOR>
 */
@DisplayName("GT06协议解码器完整功能测试")
@SpringBootTest
@ActiveProfiles("test")
public class GT06ProtocolDecoderTest {

    private EmbeddedChannel channel;

    @BeforeEach
    void setUp() {
        // 在每个测试前初始化EmbeddedChannel，并添加被测试的解码器
        channel = new EmbeddedChannel(new GT06ProtocolDecoder());
    }

    @Test
    @DisplayName("P0: 登录包(0x01)解码测试")
    void testDecodeLoginPacket() {
        // 使用concox-master成功解析的登录包数据
        String hexData = "78781101035873905215859020203201001f49170d0a";
        assertTrue(channel.writeInbound(Unpooled.wrappedBuffer(hexStringToBytes(hexData))));

        IotMessage message = channel.readInbound();
        assertNotNull(message, "应该解码出1个消息");
        assertEquals((byte) 0x01, message.getProtocol(), "协议号应该是0x01(登录包)");
        assertEquals("0358739052158590", message.getImei(), "IMEI解析不正确");
        assertEquals(31, message.getSequenceNumber(), "序列号解析不正确");
    }

    @Test
    @DisplayName("P0: 心跳包(0x23)解码测试")
    void testDecodeHeartbeatPacket() {
        // 心跳包数据
        String hexData = "78780b23c001010001000818720d0a";
        assertTrue(channel.writeInbound(Unpooled.wrappedBuffer(hexStringToBytes(hexData))));

        IotMessage message = channel.readInbound();
        assertNotNull(message, "应该解码出1个消息");
        assertEquals((byte) 0x23, message.getProtocol(), "协议号应该是0x23(心跳包)");
        assertNotNull(message.getStatusData(), "状态数据不应为空");
        assertTrue(message.getStatusData().isAccHigh(), "ACC状态应为高");
        assertFalse(message.getStatusData().isGpsPositioning(), "GPS定位状态应为未定位");
    }

    @Test
    @DisplayName("P0: GPS定位包(0x12)解码测试")
    void testDecodeLocationPacket() {
        // 定位数据包
        String hexData = "78782212140c1a0c283109027ac2c00c375540000100018a3501020304050607080d0a";
        assertTrue(channel.writeInbound(Unpooled.wrappedBuffer(hexStringToBytes(hexData))));

        IotMessage message = channel.readInbound();
        assertNotNull(message, "应该解码出1个消息");
        assertEquals((byte) 0x12, message.getProtocol(), "协议号应该是0x12(定位包)");
        assertNotNull(message.getGpsData(), "GPS数据不应为空");
        assertEquals(LocalDateTime.of(2020, 12, 26, 12, 40, 49), message.getGpsData().getDatetime());
        assertEquals(9, message.getGpsData().getSatellites());
        assertEquals(22.572000, message.getGpsData().getLatitude(), 0.000001);
        assertEquals(113.925000, message.getGpsData().getLongitude(), 0.000001);
    }

    @Test
    @DisplayName("P1: 状态信息包(0x13)解码测试")
    void testDecodeStatusInfoPacket() {
        String hexData = "78780513c0010102030d0a";
        assertTrue(channel.writeInbound(Unpooled.wrappedBuffer(hexStringToBytes(hexData))));
        IotMessage message = channel.readInbound();
        assertNotNull(message, "应该解码出1个消息");
        assertEquals((byte) 0x13, message.getProtocol(), "协议号应该是0x13(状态信息包)");
        assertNotNull(message.getStatusData(), "状态数据不应为空");
        assertTrue(message.getStatusData().isAccHigh(), "ACC状态应为高");
        assertFalse(message.getStatusData().isGpsPositioning(), "GPS定位状态应为未定位");
    }


    @Test
    @DisplayName("P1: 报警包(0x26)解码测试")
    void testDecodeAlarmPacket() {
        // 复合报警包数据
        String hexData = "7878332614010101010109027ac2c00c3755400001012c01012c0101c00101000100020003000400050006000700080009000a000b000c000d000e000f0d0a";
        assertTrue(channel.writeInbound(Unpooled.wrappedBuffer(hexStringToBytes(hexData))));

        IotMessage message = channel.readInbound();
        assertNotNull(message, "应该解码出1个消息");
        assertEquals((byte) 0x26, message.getProtocol(), "协议号应该是0x26(报警包)");
        assertNotNull(message.getGpsData(), "GPS数据不应为空");
        assertNotNull(message.getLbsData(), "LBS数据不应为空");
        assertNotNull(message.getStatusData(), "状态数据不应为空");
        assertNotNull(message.getAlarmData(), "报警数据不应为空");
    }

    @Test
    @DisplayName("P2: 组合信息包(0x16)解码测试")
    void testDecodeCombinedInfoPacket() {
        // 组合信息包数据 (比报警包少最后一个报警字节)
        String hexData = "78781f1614010101010109027ac2c00c3755400001012c01012c0101c00100010d0a";
        assertTrue(channel.writeInbound(Unpooled.wrappedBuffer(hexStringToBytes(hexData))));

        IotMessage message = channel.readInbound();
        assertNotNull(message, "应该解码出1个消息");
        assertEquals((byte) 0x16, message.getProtocol(), "协议号应该是0x16(组合信息包)");
        assertNotNull(message.getGpsData(), "GPS数据不应为空");
        assertNotNull(message.getLbsData(), "LBS数据不应为空");
        assertNotNull(message.getStatusData(), "状态数据不应为空");
        assertNull(message.getAlarmData(), "报警数据应为空");
    }

    @Test
    @DisplayName("P2: LBS信息包(0x11)解码测试")
    void testDecodeLbsInfoPacket() {
        // LBS信息包 (4字节CellID)
        String hexData = "78780d1101230405060708090a0b0c0d0a";
        assertTrue(channel.writeInbound(Unpooled.wrappedBuffer(hexStringToBytes(hexData))));

        IotMessage message = channel.readInbound();
        assertNotNull(message, "应该解码出1个消息");
        assertEquals((byte) 0x11, message.getProtocol(), "协议号应该是0x11(LBS信息包)");
        assertNotNull(message.getLbsData(), "LBS数据不应为空");
        assertEquals(0x0123, message.getLbsData().getMcc());
        assertEquals(0x04, message.getLbsData().getMnc());
        assertEquals(0x0506, message.getLbsData().getLac());
        assertEquals(0x0708090a, message.getLbsData().getCellId());
    }

    @Test
    @DisplayName("P2: IMSI上报包(0x90)解码测试")
    void testDecodeImsiPacket() {
        // IMSI: "123456789012345" -> 313233343536373839303132333435
        String hexData = "7878139031323334353637383930313233343500010d0a";
        assertTrue(channel.writeInbound(Unpooled.wrappedBuffer(hexStringToBytes(hexData))));

        IotMessage message = channel.readInbound();
        assertNotNull(message, "应该解码出1个消息");
        assertEquals((byte) 0x90, message.getProtocol(), "协议号应该是0x90(IMSI上报包)");
        assertEquals("123456789012345", message.getImsi());
    }

    @Test
    @DisplayName("P2: ICCID上报包(0x94)解码测试")
    void testDecodeIccidPacket() {
        // ICCID: "12345" -> 3132333435
        String hexData = "78780994313233343500010d0a";
        assertTrue(channel.writeInbound(Unpooled.wrappedBuffer(hexStringToBytes(hexData))));

        IotMessage message = channel.readInbound();
        assertNotNull(message, "应该解码出1个消息");
        assertEquals((byte) 0x94, message.getProtocol(), "协议号应该是0x94(ICCID上报包)");
        assertEquals("12345", message.getIccid());
    }

    @Test
    @DisplayName("P2: LBS+WIFI信息包(0x2C)解码测试")
    void testDecodeLbsWifiPacket() {
        // LBS(9) + WifiCount(1) + Wifi1(7) + Wifi2(7)
        String hexData = "78781c2c01230405060708090a02" +
            "aabbccddeeff11" + // MAC 1 + RSSI 1
            "11223344556622" + // MAC 2 + RSSI 2
            "00010d0a";
        assertTrue(channel.writeInbound(Unpooled.wrappedBuffer(hexStringToBytes(hexData))));

        IotMessage message = channel.readInbound();
        assertNotNull(message, "应该解码出1个消息");
        assertEquals((byte) 0x2C, message.getProtocol(), "协议号应该是0x2C(LBS+WIFI信息包)");
        assertNotNull(message.getLbsData(), "LBS数据不应为空");
        assertNotNull(message.getWifiHotspots(), "WIFI热点列表不应为空");
        assertEquals(2, message.getWifiHotspots().size());
        assertEquals("AA:BB:CC:DD:EE:FF", message.getWifiHotspots().get(0).getMacAddress());
        assertEquals(0x11, message.getWifiHotspots().get(0).getSignalStrength());
        assertEquals("11:22:33:44:55:66", message.getWifiHotspots().get(1).getMacAddress());
        assertEquals(0x22, message.getWifiHotspots().get(1).getSignalStrength());
    }

    @Test
    @DisplayName("P2: 终端指令响应包(0x15)解码测试")
    void testDecodeCommandResponsePacket() {
        // ServerFlag(4) + CommandContent(ASCII "OK")
        String hexData = "78780a15010203044F4B00010d0a";
        assertTrue(channel.writeInbound(Unpooled.wrappedBuffer(hexStringToBytes(hexData))));

        IotMessage message = channel.readInbound();
        assertNotNull(message, "应该解码出1个消息");
        assertEquals((byte) 0x15, message.getProtocol(), "协议号应该是0x15(终端指令响应包)");
        assertNotNull(message.getCommandResponseData(), "指令响应数据不应为空");
        assertEquals(0x01020304L, message.getCommandResponseData().getServerFlag());
        assertEquals("OK", message.getCommandResponseData().getCommandContent());
    }


    @Test
    @DisplayName("健壮性: 测试数据不足情况")
    void testInsufficientData() {
        String hexData = "787811"; // 不完整的数据包
        assertFalse(channel.writeInbound(Unpooled.wrappedBuffer(hexStringToBytes(hexData))));
        assertNull(channel.readInbound(), "数据不足时不应该产生消息");
    }

    @Test
    @DisplayName("健壮性: 测试无效起始位处理")
    void testInvalidStartFlag() {
        String hexData = "1234" + "78781101035873905215859020203201001f49170d0a"; // 前缀无效数据
        assertTrue(channel.writeInbound(Unpooled.wrappedBuffer(hexStringToBytes(hexData))));
        assertNotNull(channel.readInbound(), "解码器应该能跳过无效数据并找到有效包");
    }

    @Test
    @DisplayName("健壮性: 测试多个数据包连续解码")
    void testMultiplePacketsDecode() {
        String packet1 = "78781101035873905215859020203201001f49170d0a"; // 登录包
        String packet2 = "78780b23c001010001000818720d0a";             // 心跳包
        String combinedHex = packet1 + packet2;

        assertTrue(channel.writeInbound(Unpooled.wrappedBuffer(hexStringToBytes(combinedHex))));

        IotMessage message1 = channel.readInbound();
        assertNotNull(message1, "应该解码出第一个消息");
        assertEquals((byte) 0x01, message1.getProtocol(), "第一个消息应该是登录包");

        IotMessage message2 = channel.readInbound();
        assertNotNull(message2, "应该解码出第二个消息");
        assertEquals((byte) 0x23, message2.getProtocol(), "第二个消息应该是心跳包");
    }

    /**
     * 将hex字符串转换为字节数组
     */
    private byte[] hexStringToBytes(String hexString) {
        hexString = hexString.replaceAll("[^0-9A-Fa-f]", "");
        if (hexString.length() % 2 != 0) {
            throw new IllegalArgumentException("Hex字符串长度必须是偶数");
        }
        byte[] bytes = new byte[hexString.length() / 2];
        for (int i = 0; i < bytes.length; i++) {
            int index = i * 2;
            bytes[i] = (byte) Integer.parseInt(hexString.substring(index, index + 2), 16);
        }
        return bytes;
    }
}